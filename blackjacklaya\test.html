<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blackjack Game Tests</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        
        .test-pass {
            background: #1a4a1a;
            border-left: 4px solid #00ff00;
        }
        
        .test-fail {
            background: #4a1a1a;
            border-left: 4px solid #ff0000;
            color: #ff6666;
        }
        
        .test-info {
            background: #1a1a4a;
            border-left: 4px solid #6666ff;
            color: #ccccff;
        }
        
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #666;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #444;
        }
        
        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-box {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
            min-width: 100px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #00ff00;
        }
        
        .stat-label {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <h1>🃏 Blackjack Game Test Suite</h1>
    
    <div class="stats">
        <div class="stat-box">
            <div class="stat-number" id="totalTests">0</div>
            <div class="stat-label">Total Tests</div>
        </div>
        <div class="stat-box">
            <div class="stat-number" id="passedTests">0</div>
            <div class="stat-label">Passed</div>
        </div>
        <div class="stat-box">
            <div class="stat-number" id="failedTests">0</div>
            <div class="stat-label">Failed</div>
        </div>
        <div class="stat-box">
            <div class="stat-number" id="successRate">0%</div>
            <div class="stat-label">Success Rate</div>
        </div>
    </div>
    
    <div>
        <button onclick="runAllTests()">🚀 Run All Tests</button>
        <button onclick="runCardTests()">🃏 Card Tests</button>
        <button onclick="runDeckTests()">🎴 Deck Tests</button>
        <button onclick="runPlayerTests()">👤 Player Tests</button>
        <button onclick="runGameTests()">🎮 Game Logic Tests</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
    </div>
    
    <div id="testResults"></div>

    <!-- Include game files -->
    <script src="js/card.js"></script>
    <script src="js/deck.js"></script>
    <script src="js/player.js"></script>
    <script src="js/game.js"></script>

    <script>
        // Test framework
        class TestFramework {
            constructor() {
                this.tests = [];
                this.results = [];
                this.currentSection = '';
            }

            section(name) {
                this.currentSection = name;
                this.log(`\n=== ${name} ===`, 'info');
            }

            test(description, testFunction) {
                try {
                    const result = testFunction();
                    if (result === true || result === undefined) {
                        this.pass(description);
                    } else {
                        this.fail(description, result);
                    }
                } catch (error) {
                    this.fail(description, error.message);
                }
            }

            assert(condition, message = 'Assertion failed') {
                if (!condition) {
                    throw new Error(message);
                }
                return true;
            }

            assertEqual(actual, expected, message = '') {
                if (actual !== expected) {
                    throw new Error(`${message} Expected: ${expected}, Got: ${actual}`);
                }
                return true;
            }

            assertNotEqual(actual, unexpected, message = '') {
                if (actual === unexpected) {
                    throw new Error(`${message} Expected not to be: ${unexpected}, Got: ${actual}`);
                }
                return true;
            }

            pass(description) {
                this.results.push({ type: 'pass', description, section: this.currentSection });
                this.log(`✅ ${description}`, 'pass');
            }

            fail(description, error = '') {
                this.results.push({ type: 'fail', description, error, section: this.currentSection });
                this.log(`❌ ${description}${error ? ': ' + error : ''}`, 'fail');
            }

            log(message, type = 'info') {
                const resultsDiv = document.getElementById('testResults');
                const div = document.createElement('div');
                div.className = `test-result test-${type}`;
                div.textContent = message;
                resultsDiv.appendChild(div);
                console.log(message);
            }

            getStats() {
                const total = this.results.length;
                const passed = this.results.filter(r => r.type === 'pass').length;
                const failed = total - passed;
                const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;
                
                return { total, passed, failed, successRate };
            }

            updateStats() {
                const stats = this.getStats();
                document.getElementById('totalTests').textContent = stats.total;
                document.getElementById('passedTests').textContent = stats.passed;
                document.getElementById('failedTests').textContent = stats.failed;
                document.getElementById('successRate').textContent = stats.successRate + '%';
            }

            clear() {
                this.results = [];
                document.getElementById('testResults').innerHTML = '';
                this.updateStats();
            }
        }

        const test = new TestFramework();

        // Card Tests
        function runCardTests() {
            test.section('Card Tests');

            test.test('Card creation with valid suit and rank', () => {
                const card = new Card('Hearts', 'A');
                test.assertEqual(card.suit, 'Hearts');
                test.assertEqual(card.rank, 'A');
                test.assertEqual(card.isHidden, false);
            });

            test.test('Card value calculation for number cards', () => {
                const card5 = new Card('Spades', '5');
                test.assertEqual(card5.getValue(), 5);
                
                const card10 = new Card('Clubs', '10');
                test.assertEqual(card10.getValue(), 10);
            });

            test.test('Card value calculation for face cards', () => {
                const jack = new Card('Hearts', 'J');
                const queen = new Card('Diamonds', 'Q');
                const king = new Card('Spades', 'K');
                
                test.assertEqual(jack.getValue(), 10);
                test.assertEqual(queen.getValue(), 10);
                test.assertEqual(king.getValue(), 10);
            });

            test.test('Card value calculation for Ace', () => {
                const ace = new Card('Hearts', 'A');
                test.assertEqual(ace.getValue(), 11);
                test.assert(ace.isAce());
            });

            test.test('Card suit symbols', () => {
                test.assertEqual(new Card('Hearts', 'A').getSuitSymbol(), '♥');
                test.assertEqual(new Card('Diamonds', 'K').getSuitSymbol(), '♦');
                test.assertEqual(new Card('Clubs', 'Q').getSuitSymbol(), '♣');
                test.assertEqual(new Card('Spades', 'J').getSuitSymbol(), '♠');
            });

            test.test('Card color classification', () => {
                test.assertEqual(new Card('Hearts', 'A').getColorClass(), 'red');
                test.assertEqual(new Card('Diamonds', 'K').getColorClass(), 'red');
                test.assertEqual(new Card('Clubs', 'Q').getColorClass(), 'black');
                test.assertEqual(new Card('Spades', 'J').getColorClass(), 'black');
            });

            test.updateStats();
        }

        // Deck Tests
        function runDeckTests() {
            test.section('Deck Tests');

            test.test('Deck initialization with correct number of cards', () => {
                const deck = new Deck(1);
                test.assertEqual(deck.getCardsRemaining(), 52);
            });

            test.test('Multi-deck initialization', () => {
                const deck = new Deck(6);
                test.assertEqual(deck.getCardsRemaining(), 312); // 6 * 52
            });

            test.test('Card dealing reduces deck size', () => {
                const deck = new Deck(1);
                const initialCount = deck.getCardsRemaining();
                const card = deck.dealCard();
                
                test.assert(card instanceof Card);
                test.assertEqual(deck.getCardsRemaining(), initialCount - 1);
            });

            test.test('Dealing multiple cards', () => {
                const deck = new Deck(1);
                const cards = deck.dealCards(5);
                
                test.assertEqual(cards.length, 5);
                test.assertEqual(deck.getCardsRemaining(), 47);
                test.assert(cards.every(card => card instanceof Card));
            });

            test.test('Deck shuffling changes card order', () => {
                const deck1 = new Deck(1);
                const deck2 = new Deck(1);
                
                // Deal a few cards from each deck
                const cards1 = deck1.dealCards(10).map(c => c.toString());
                const cards2 = deck2.dealCards(10).map(c => c.toString());
                
                // They should be different (very high probability)
                test.assertNotEqual(JSON.stringify(cards1), JSON.stringify(cards2));
            });

            test.test('Discard pile management', () => {
                const deck = new Deck(1);
                const card = deck.dealCard();
                
                test.assertEqual(deck.getDiscardedCount(), 0);
                deck.discardCard(card);
                test.assertEqual(deck.getDiscardedCount(), 1);
            });

            test.updateStats();
        }

        // Player Tests
        function runPlayerTests() {
            test.section('Player Tests');

            test.test('Player initialization', () => {
                const player = new Player('Test Player');
                test.assertEqual(player.name, 'Test Player');
                test.assertEqual(player.hand.length, 0);
                test.assertEqual(player.chips, 1000);
                test.assertEqual(player.isDealer, false);
            });

            test.test('Dealer initialization', () => {
                const dealer = new Player('Dealer', true);
                test.assertEqual(dealer.isDealer, true);
                test.assertEqual(dealer.chips, 0);
            });

            test.test('Adding cards to hand', () => {
                const player = new Player('Test');
                const card = new Card('Hearts', 'A');
                
                player.addCard(card);
                test.assertEqual(player.hand.length, 1);
                test.assertEqual(player.hand[0], card);
            });

            test.test('Score calculation with number cards', () => {
                const player = new Player('Test');
                player.addCard(new Card('Hearts', '5'));
                player.addCard(new Card('Spades', '7'));
                
                test.assertEqual(player.getScore(), 12);
            });

            test.test('Score calculation with face cards', () => {
                const player = new Player('Test');
                player.addCard(new Card('Hearts', 'K'));
                player.addCard(new Card('Spades', 'Q'));
                
                test.assertEqual(player.getScore(), 20);
            });

            test.test('Ace handling - soft hand', () => {
                const player = new Player('Test');
                player.addCard(new Card('Hearts', 'A'));
                player.addCard(new Card('Spades', '6'));
                
                test.assertEqual(player.getScore(), 17); // Ace as 11
            });

            test.test('Ace handling - hard hand', () => {
                const player = new Player('Test');
                player.addCard(new Card('Hearts', 'A'));
                player.addCard(new Card('Spades', '6'));
                player.addCard(new Card('Clubs', '8'));
                
                test.assertEqual(player.getScore(), 15); // Ace as 1
            });

            test.test('Multiple Aces handling', () => {
                const player = new Player('Test');
                player.addCard(new Card('Hearts', 'A'));
                player.addCard(new Card('Spades', 'A'));
                player.addCard(new Card('Clubs', '9'));
                
                test.assertEqual(player.getScore(), 21); // One Ace as 11, one as 1
            });

            test.test('Blackjack detection', () => {
                const player = new Player('Test');
                player.addCard(new Card('Hearts', 'A'));
                player.addCard(new Card('Spades', 'K'));
                
                test.assert(player.checkBlackjack());
                test.assertEqual(player.getScore(), 21);
            });

            test.test('Bust detection', () => {
                const player = new Player('Test');
                player.addCard(new Card('Hearts', 'K'));
                player.addCard(new Card('Spades', 'Q'));
                player.addCard(new Card('Clubs', '5'));
                
                test.assert(player.checkBusted());
                test.assertEqual(player.getScore(), 25);
            });

            test.test('Betting system', () => {
                const player = new Player('Test');
                
                test.assert(player.placeBet(100));
                test.assertEqual(player.chips, 900);
                test.assertEqual(player.currentBet, 100);
                
                test.assert(!player.placeBet(1000)); // Should fail - insufficient chips
            });

            test.updateStats();
        }

        // Game Logic Tests
        function runGameTests() {
            test.section('Game Logic Tests');

            test.test('Game initialization', () => {
                // Note: This test might not work in this context since it requires DOM elements
                // But we can test the basic structure
                test.assert(typeof BlackjackGame === 'function');
            });

            test.test('Dealer hitting rules - must hit on 16', () => {
                const dealer = new Player('Dealer', true);
                dealer.addCard(new Card('Hearts', '10'));
                dealer.addCard(new Card('Spades', '6'));
                
                test.assert(dealer.shouldDealerHit());
            });

            test.test('Dealer hitting rules - must stand on 17', () => {
                const dealer = new Player('Dealer', true);
                dealer.addCard(new Card('Hearts', '10'));
                dealer.addCard(new Card('Spades', '7'));
                
                test.assert(!dealer.shouldDealerHit());
            });

            test.test('Dealer hitting rules - must hit on soft 17', () => {
                const dealer = new Player('Dealer', true);
                dealer.addCard(new Card('Hearts', 'A'));
                dealer.addCard(new Card('Spades', '6'));
                
                test.assert(dealer.shouldDealerHit());
            });

            test.updateStats();
        }

        // Run all tests
        function runAllTests() {
            test.clear();
            runCardTests();
            runDeckTests();
            runPlayerTests();
            runGameTests();
            
            const stats = test.getStats();
            test.log(`\n🏁 All tests completed! ${stats.passed}/${stats.total} passed (${stats.successRate}%)`, 'info');
        }

        function clearResults() {
            test.clear();
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            test.log('🃏 Blackjack Game Test Suite Loaded', 'info');
            test.log('Click "Run All Tests" to start testing', 'info');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blackjack Game - Laya Air</title>
    <meta name="description" content="Professional blackjack card game built with Laya Air engine featuring realistic gameplay, smooth animations, and responsive design">
    <meta name="keywords" content="blackjack, card game, casino, 21, laya air, html5 game, online blackjack">
    <link rel="stylesheet" href="css/game.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0f4c3a, #1a5f4a);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #gameContainer {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
        }
        
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .game-ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .game-ui > * {
            pointer-events: auto;
        }
        
        .dealer-area {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: white;
        }
        
        .player-area {
            position: absolute;
            bottom: 150px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: white;
        }
        
        .controls {
            position: absolute;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
        }
        
        .btn {
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-hit {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .btn-stand {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }
        
        .btn-deal {
            background: linear-gradient(45deg, #27ae60, #229954);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .score {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .cards-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            min-height: 120px;
        }
        
        .card {
            width: 80px;
            height: 120px;
            background: white;
            border-radius: 10px;
            border: 2px solid #333;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 8px;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }
        
        .card.red {
            color: #e74c3c;
        }
        
        .card.black {
            color: #2c3e50;
        }
        
        .card-back {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .game-status {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            display: none;
        }
        
        .chips {
            position: absolute;
            bottom: 200px;
            right: 50px;
            color: white;
            font-size: 18px;
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
                margin: 5px 0;
            }
            
            .card {
                width: 60px;
                height: 90px;
                font-size: 12px;
            }
            
            .chips {
                right: 20px;
                bottom: 250px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <div>Loading Blackjack Game...</div>
        </div>
        
        <div class="game-ui" id="gameUI" style="display: none;">
            <div class="dealer-area">
                <h2>Dealer</h2>
                <div class="score" id="dealerScore">Score: 0</div>
                <div class="cards-container" id="dealerCards"></div>
            </div>
            
            <div class="player-area">
                <h2>Player</h2>
                <div class="score" id="playerScore">Score: 0</div>
                <div class="cards-container" id="playerCards"></div>
            </div>
            
            <div class="controls">
                <button class="btn btn-hit" id="hitBtn">Hit</button>
                <button class="btn btn-stand" id="standBtn">Stand</button>
                <button class="btn btn-deal" id="dealBtn">New Game</button>
            </div>
            
            <div class="chips">
                <div>Chips: $<span id="chipCount">1000</span></div>
                <div>Bet: $<span id="currentBet">50</span></div>
            </div>
            
            <div class="game-status" id="gameStatus">
                <div id="statusMessage"></div>
                <button class="btn btn-deal" onclick="game.newGame()">Play Again</button>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/card.js"></script>
    <script src="js/deck.js"></script>
    <script src="js/player.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>

/**
 * Game Configuration
 * Central configuration file for blackjack game settings
 */

const GameConfig = {
    // Game Rules
    RULES: {
        // Number of decks in the shoe
        DECK_COUNT: 6,
        
        // Dealer hits on soft 17
        DEALER_HITS_SOFT_17: true,
        
        // Blackjack payout ratio (3:2 = 1.5, 6:5 = 1.2)
        BLACKJACK_PAYOUT: 1.5,
        
        // Regular win payout ratio
        WIN_PAYOUT: 1.0,
        
        // Minimum cards before reshuffling (percentage of total deck)
        RESHUFFLE_THRESHOLD: 0.2,
        
        // Double down allowed
        ALLOW_DOUBLE_DOWN: true,
        
        // Split pairs allowed
        ALLOW_SPLIT: false, // Not implemented yet
        
        // Insurance allowed
        ALLOW_INSURANCE: false, // Not implemented yet
        
        // Surrender allowed
        ALLOW_SURRENDER: false // Not implemented yet
    },

    // Player Settings
    PLAYER: {
        // Starting chip amount
        STARTING_CHIPS: 1000,
        
        // Default bet amount
        DEFAULT_BET: 50,
        
        // Minimum bet
        MIN_BET: 10,
        
        // Maximum bet (0 = no limit)
        MAX_BET: 0,
        
        // Available bet amounts for quick selection
        QUICK_BETS: [10, 25, 50, 100, 250, 500]
    },

    // Animation Settings
    ANIMATIONS: {
        // Card dealing animation duration (ms)
        DEAL_DURATION: 500,
        
        // Delay between dealing cards (ms)
        DEAL_DELAY: 300,
        
        // Card flip animation duration (ms)
        FLIP_DURATION: 600,
        
        // Button hover animation duration (ms)
        BUTTON_HOVER_DURATION: 200,
        
        // Score update animation duration (ms)
        SCORE_ANIMATION_DURATION: 300,
        
        // Game result display duration (ms)
        RESULT_DISPLAY_DURATION: 3000,
        
        // Enable/disable all animations
        ENABLED: true,
        
        // Reduced motion for accessibility
        RESPECT_REDUCED_MOTION: true
    },

    // UI Settings
    UI: {
        // Show card count in deck
        SHOW_DECK_COUNT: false,
        
        // Show game statistics
        SHOW_STATISTICS: true,
        
        // Show keyboard shortcuts help
        SHOW_KEYBOARD_HELP: true,
        
        // Auto-hide game result after duration
        AUTO_HIDE_RESULT: true,
        
        // Theme selection
        THEME: 'default', // 'default', 'dark', 'classic'
        
        // Card back design
        CARD_BACK_STYLE: 'blue', // 'blue', 'red', 'green', 'black'
        
        // Show card values on hover
        SHOW_CARD_VALUES: false,
        
        // Compact mode for small screens
        COMPACT_MODE_THRESHOLD: 768 // pixels
    },

    // Audio Settings
    AUDIO: {
        // Enable sound effects
        ENABLED: false, // Disabled by default (not implemented)
        
        // Master volume (0.0 to 1.0)
        MASTER_VOLUME: 0.7,
        
        // Individual sound volumes
        VOLUMES: {
            CARD_DEAL: 0.5,
            CARD_FLIP: 0.4,
            BUTTON_CLICK: 0.3,
            WIN: 0.8,
            LOSE: 0.6,
            BLACKJACK: 1.0,
            SHUFFLE: 0.4
        },
        
        // Sound file paths (when implemented)
        SOUNDS: {
            CARD_DEAL: 'audio/card_deal.mp3',
            CARD_FLIP: 'audio/card_flip.mp3',
            BUTTON_CLICK: 'audio/button_click.mp3',
            WIN: 'audio/win.mp3',
            LOSE: 'audio/lose.mp3',
            BLACKJACK: 'audio/blackjack.mp3',
            SHUFFLE: 'audio/shuffle.mp3'
        }
    },

    // Performance Settings
    PERFORMANCE: {
        // Enable performance monitoring
        MONITOR_FPS: false,
        
        // Target frame rate
        TARGET_FPS: 60,
        
        // Enable GPU acceleration for animations
        USE_GPU_ACCELERATION: true,
        
        // Debounce rapid button clicks (ms)
        BUTTON_DEBOUNCE: 100,
        
        // Auto-save interval (ms)
        AUTO_SAVE_INTERVAL: 30000,
        
        // Maximum game history entries to keep
        MAX_HISTORY_ENTRIES: 1000
    },

    // Development Settings
    DEBUG: {
        // Enable debug mode
        ENABLED: false,
        
        // Show console logs
        CONSOLE_LOGS: true,
        
        // Show performance metrics
        SHOW_PERFORMANCE: false,
        
        // Enable test mode features
        TEST_MODE: false,
        
        // Skip animations in test mode
        SKIP_ANIMATIONS_IN_TEST: true,
        
        // Force specific cards (for testing)
        FORCE_CARDS: null // Array of {suit, rank} objects
    },

    // Accessibility Settings
    ACCESSIBILITY: {
        // High contrast mode
        HIGH_CONTRAST: false,
        
        // Large text mode
        LARGE_TEXT: false,
        
        // Screen reader announcements
        SCREEN_READER_ENABLED: true,
        
        // Keyboard navigation
        KEYBOARD_NAVIGATION: true,
        
        // Focus indicators
        SHOW_FOCUS_INDICATORS: true,
        
        // Color blind friendly mode
        COLOR_BLIND_FRIENDLY: false
    },

    // Storage Settings
    STORAGE: {
        // Local storage key prefix
        KEY_PREFIX: 'blackjack_',
        
        // Auto-save game state
        AUTO_SAVE: true,
        
        // Save game statistics
        SAVE_STATISTICS: true,
        
        // Save user preferences
        SAVE_PREFERENCES: true,
        
        // Data retention period (days)
        RETENTION_DAYS: 30
    },

    // Network Settings (for future multiplayer)
    NETWORK: {
        // Enable online features
        ENABLED: false,
        
        // Server URL
        SERVER_URL: '',
        
        // Connection timeout (ms)
        TIMEOUT: 5000,
        
        // Retry attempts
        MAX_RETRIES: 3
    }
};

// Utility functions for configuration
const ConfigUtils = {
    /**
     * Get a configuration value by path
     * @param {string} path - Dot notation path (e.g., 'RULES.DECK_COUNT')
     * @returns {*} Configuration value
     */
    get(path) {
        return path.split('.').reduce((obj, key) => obj && obj[key], GameConfig);
    },

    /**
     * Set a configuration value by path
     * @param {string} path - Dot notation path
     * @param {*} value - Value to set
     */
    set(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, key) => obj[key], GameConfig);
        if (target) {
            target[lastKey] = value;
        }
    },

    /**
     * Load configuration from localStorage
     */
    loadFromStorage() {
        try {
            const stored = localStorage.getItem(GameConfig.STORAGE.KEY_PREFIX + 'config');
            if (stored) {
                const config = JSON.parse(stored);
                Object.assign(GameConfig, config);
            }
        } catch (error) {
            console.warn('Failed to load configuration from storage:', error);
        }
    },

    /**
     * Save configuration to localStorage
     */
    saveToStorage() {
        try {
            localStorage.setItem(
                GameConfig.STORAGE.KEY_PREFIX + 'config',
                JSON.stringify(GameConfig)
            );
        } catch (error) {
            console.warn('Failed to save configuration to storage:', error);
        }
    },

    /**
     * Reset configuration to defaults
     */
    reset() {
        // This would require storing the original defaults
        console.log('Configuration reset to defaults');
    },

    /**
     * Validate configuration values
     * @returns {boolean} True if configuration is valid
     */
    validate() {
        const errors = [];

        // Validate deck count
        if (GameConfig.RULES.DECK_COUNT < 1 || GameConfig.RULES.DECK_COUNT > 8) {
            errors.push('DECK_COUNT must be between 1 and 8');
        }

        // Validate payout ratios
        if (GameConfig.RULES.BLACKJACK_PAYOUT <= 0) {
            errors.push('BLACKJACK_PAYOUT must be positive');
        }

        // Validate starting chips
        if (GameConfig.PLAYER.STARTING_CHIPS <= 0) {
            errors.push('STARTING_CHIPS must be positive');
        }

        // Validate bet amounts
        if (GameConfig.PLAYER.DEFAULT_BET < GameConfig.PLAYER.MIN_BET) {
            errors.push('DEFAULT_BET cannot be less than MIN_BET');
        }

        if (errors.length > 0) {
            console.error('Configuration validation errors:', errors);
            return false;
        }

        return true;
    }
};

// Auto-load configuration on script load
if (typeof window !== 'undefined') {
    ConfigUtils.loadFromStorage();
    
    // Validate configuration
    if (!ConfigUtils.validate()) {
        console.warn('Configuration validation failed, using defaults');
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GameConfig, ConfigUtils };
}

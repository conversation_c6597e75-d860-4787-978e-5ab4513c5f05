/**
 * Main entry point for Blackjack game
 * Handles initialization, loading, and global game management
 */

// Global game instance
let game = null;

// Game configuration
const CONFIG = {
    ANIMATION_SPEED: 500,
    DEAL_DELAY: 300,
    AUTO_SAVE: true,
    SOUND_ENABLED: true,
    DEBUG_MODE: false
};

/**
 * Initialize the game when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeGame();
});

/**
 * Initialize game and UI
 */
function initializeGame() {
    try {
        // Show loading screen
        showLoading();
        
        // Simulate loading time for better UX
        setTimeout(() => {
            // Create game instance
            game = new BlackjackGame();
            
            // Load saved game state if available
            loadGameState();
            
            // Hide loading and show game
            hideLoading();
            showGame();
            
            // Add welcome message
            showWelcomeMessage();
            
            console.log('Blackjack game initialized successfully');
            
        }, 1500);
        
    } catch (error) {
        console.error('Failed to initialize game:', error);
        showError('Failed to load game. Please refresh the page.');
    }
}

/**
 * Show loading screen
 */
function showLoading() {
    const loading = document.getElementById('loading');
    const gameUI = document.getElementById('gameUI');
    
    if (loading) loading.style.display = 'block';
    if (gameUI) gameUI.style.display = 'none';
}

/**
 * Hide loading screen
 */
function hideLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.style.opacity = '0';
        setTimeout(() => {
            loading.style.display = 'none';
        }, 500);
    }
}

/**
 * Show game UI
 */
function showGame() {
    const gameUI = document.getElementById('gameUI');
    if (gameUI) {
        gameUI.style.display = 'block';
        gameUI.style.opacity = '0';
        setTimeout(() => {
            gameUI.style.opacity = '1';
        }, 100);
    }
}

/**
 * Show welcome message
 */
function showWelcomeMessage() {
    const welcomeMsg = `
        🎰 Welcome to Professional Blackjack! 🎰
        
        🎯 Goal: Get as close to 21 as possible without going over
        🃏 Face cards (J, Q, K) = 10 points
        🅰️ Aces = 1 or 11 points (automatically optimized)
        
        🎮 Controls:
        • Click "Hit" or press H/Space to take a card
        • Click "Stand" or press S/Enter to stop
        • Press N for new game
        
        💰 Starting chips: $1000
        💵 Default bet: $50
        
        Good luck! 🍀
    `;
    
    console.log(welcomeMsg);
    
    // Show brief tutorial overlay (optional)
    if (isFirstTime()) {
        showTutorial();
    }
}

/**
 * Check if this is user's first time playing
 */
function isFirstTime() {
    return !localStorage.getItem('blackjack_played_before');
}

/**
 * Show tutorial overlay
 */
function showTutorial() {
    // Mark as played
    localStorage.setItem('blackjack_played_before', 'true');
    
    // Could implement tutorial overlay here
    console.log('Tutorial: This is your first time playing!');
}

/**
 * Load saved game state from localStorage
 */
function loadGameState() {
    try {
        const savedState = localStorage.getItem('blackjack_game_state');
        if (savedState && CONFIG.AUTO_SAVE) {
            const state = JSON.parse(savedState);
            
            // Restore player chips
            if (state.playerChips && game) {
                game.player.chips = state.playerChips;
            }
            
            // Restore game history
            if (state.gameHistory && game) {
                game.gameHistory = state.gameHistory || [];
            }
            
            console.log('Game state loaded successfully');
        }
    } catch (error) {
        console.warn('Failed to load saved game state:', error);
    }
}

/**
 * Save game state to localStorage
 */
function saveGameState() {
    if (!CONFIG.AUTO_SAVE || !game) return;
    
    try {
        const state = {
            playerChips: game.player.chips,
            gameHistory: game.gameHistory,
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('blackjack_game_state', JSON.stringify(state));
        console.log('Game state saved');
    } catch (error) {
        console.warn('Failed to save game state:', error);
    }
}

/**
 * Auto-save game state periodically
 */
setInterval(() => {
    if (game && CONFIG.AUTO_SAVE) {
        saveGameState();
    }
}, 30000); // Save every 30 seconds

/**
 * Save game state when page is about to unload
 */
window.addEventListener('beforeunload', () => {
    saveGameState();
});

/**
 * Show error message
 */
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #e74c3c;
        color: white;
        padding: 20px;
        border-radius: 10px;
        font-size: 18px;
        z-index: 10000;
        text-align: center;
    `;
    errorDiv.textContent = message;
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
        document.body.removeChild(errorDiv);
    }, 5000);
}

/**
 * Handle window resize for responsive design
 */
window.addEventListener('resize', () => {
    // Adjust game layout if needed
    adjustGameLayout();
});

/**
 * Adjust game layout for different screen sizes
 */
function adjustGameLayout() {
    const gameContainer = document.getElementById('gameContainer');
    if (!gameContainer) return;
    
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    // Adjust for mobile devices
    if (width < 768) {
        gameContainer.classList.add('mobile-layout');
    } else {
        gameContainer.classList.remove('mobile-layout');
    }
    
    // Adjust for very small screens
    if (height < 600) {
        gameContainer.classList.add('compact-layout');
    } else {
        gameContainer.classList.remove('compact-layout');
    }
}

/**
 * Debug functions (only available in debug mode)
 */
if (CONFIG.DEBUG_MODE) {
    window.debugGame = {
        getGameState: () => game ? game.gameState : 'No game instance',
        getPlayerHand: () => game ? game.player.hand.map(card => card.toString()) : [],
        getDealerHand: () => game ? game.dealer.hand.map(card => card.toString()) : [],
        getStats: () => game ? game.getGameStats() : {},
        setChips: (amount) => {
            if (game) {
                game.player.chips = amount;
                game.updateUI();
            }
        },
        forceBust: () => {
            if (game && game.gameState === 'playing') {
                // Add cards to force bust
                const card1 = new Card('Hearts', 'K');
                const card2 = new Card('Spades', 'K');
                game.player.addCard(card1);
                game.player.addCard(card2);
                game.renderCard(card1, game.player);
                game.renderCard(card2, game.player);
                game.updateUI();
            }
        }
    };
    
    console.log('Debug mode enabled. Use window.debugGame for debugging functions.');
}

/**
 * Performance monitoring
 */
function monitorPerformance() {
    if (performance && performance.mark) {
        performance.mark('game-start');
        
        // Monitor frame rate
        let frameCount = 0;
        let lastTime = performance.now();
        
        function countFrames() {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                if (CONFIG.DEBUG_MODE) {
                    console.log(`FPS: ${fps}`);
                }
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(countFrames);
        }
        
        requestAnimationFrame(countFrames);
    }
}

// Start performance monitoring in debug mode
if (CONFIG.DEBUG_MODE) {
    monitorPerformance();
}

/**
 * Global error handler
 */
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    
    if (CONFIG.DEBUG_MODE) {
        showError(`Error: ${event.error.message}`);
    }
});

/**
 * Handle visibility change (tab switching)
 */
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Game is hidden, pause if needed
        saveGameState();
    } else {
        // Game is visible again, resume if needed
        console.log('Game resumed');
    }
});

// Initialize layout adjustment
adjustGameLayout();

console.log('Blackjack game main.js loaded successfully');

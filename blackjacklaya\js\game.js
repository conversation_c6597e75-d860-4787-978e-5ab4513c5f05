/**
 * BlackjackGame class - Main game logic and state management
 * Handles game flow, UI updates, and player interactions
 */
class BlackjackGame {
    constructor() {
        this.deck = new Deck(GameConfig.RULES.DECK_COUNT);
        this.player = new Player('Player');
        this.dealer = new Player('Dealer', true);
        this.gameState = 'waiting'; // waiting, dealing, playing, dealer_turn, game_over
        this.gameHistory = [];
        this.soundEnabled = GameConfig.AUDIO.ENABLED;

        // Set player starting chips
        this.player.chips = GameConfig.PLAYER.STARTING_CHIPS;

        this.initializeUI();
        this.bindEvents();
    }

    /**
     * Initialize UI elements and references
     */
    initializeUI() {
        this.elements = {
            playerCards: document.getElementById('playerCards'),
            dealerCards: document.getElementById('dealerCards'),
            playerScore: document.getElementById('playerScore'),
            dealerScore: document.getElementById('dealerScore'),
            hitBtn: document.getElementById('hitBtn'),
            standBtn: document.getElementById('standBtn'),
            dealBtn: document.getElementById('dealBtn'),
            gameStatus: document.getElementById('gameStatus'),
            statusMessage: document.getElementById('statusMessage'),
            chipCount: document.getElementById('chipCount'),
            currentBet: document.getElementById('currentBet')
        };
        
        this.updateUI();
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        this.elements.hitBtn.addEventListener('click', () => this.hit());
        this.elements.standBtn.addEventListener('click', () => this.stand());
        this.elements.dealBtn.addEventListener('click', () => this.newGame());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (this.gameState === 'playing') {
                switch(e.key.toLowerCase()) {
                    case 'h':
                    case ' ':
                        e.preventDefault();
                        this.hit();
                        break;
                    case 's':
                    case 'enter':
                        e.preventDefault();
                        this.stand();
                        break;
                }
            }
            if (e.key.toLowerCase() === 'n' || e.key === 'Enter') {
                if (this.gameState === 'waiting' || this.gameState === 'game_over') {
                    this.newGame();
                }
            }
        });
    }

    /**
     * Start a new game
     */
    newGame() {
        // Reset players
        this.player.resetHand();
        this.dealer.resetHand();
        
        // Place default bet
        if (!this.player.placeBet(GameConfig.PLAYER.DEFAULT_BET)) {
            this.showMessage('Insufficient chips!', 'error');
            return;
        }
        
        // Reset deck if needed
        if (this.deck.needsReshuffle()) {
            this.deck.reset();
            this.showMessage('Shuffling new deck...', 'info');
        }
        
        this.gameState = 'dealing';
        this.updateUI();
        
        // Deal initial cards with animation
        this.dealInitialCards();
    }

    /**
     * Deal initial two cards to each player
     */
    async dealInitialCards() {
        const dealDelay = GameConfig.ANIMATIONS.DEAL_DELAY;
        
        // Clear card containers
        this.elements.playerCards.innerHTML = '';
        this.elements.dealerCards.innerHTML = '';
        
        // Deal first card to player
        await this.dealCardToPlayer(this.player, false, 0);
        
        // Deal first card to dealer (face up)
        await this.dealCardToPlayer(this.dealer, false, dealDelay);
        
        // Deal second card to player
        await this.dealCardToPlayer(this.player, false, dealDelay * 2);
        
        // Deal second card to dealer (face down)
        await this.dealCardToPlayer(this.dealer, true, dealDelay * 3);
        
        // Check for blackjacks after dealing
        setTimeout(() => {
            this.checkInitialBlackjacks();
        }, dealDelay * 4);
    }

    /**
     * Deal a card to a specific player with animation
     */
    async dealCardToPlayer(player, hidden = false, delay = 0) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const card = this.deck.dealCard(hidden);
                if (card) {
                    player.addCard(card);
                    this.renderCard(card, player);
                    card.addDealAnimation();
                }
                resolve();
            }, delay);
        });
    }

    /**
     * Render a card in the UI
     */
    renderCard(card, player) {
        const cardElement = card.createElement();
        const container = player.isDealer ? this.elements.dealerCards : this.elements.playerCards;
        container.appendChild(cardElement);
        this.updateScores();
    }

    /**
     * Check for initial blackjacks
     */
    checkInitialBlackjacks() {
        const playerBlackjack = this.player.checkBlackjack();
        const dealerBlackjack = this.dealer.checkBlackjack();
        
        if (playerBlackjack || dealerBlackjack) {
            // Reveal dealer's hidden card
            this.dealer.revealAllCards();
            this.updateUI();
            
            if (playerBlackjack && dealerBlackjack) {
                this.endGame('push', 'Both have Blackjack! Push!');
            } else if (playerBlackjack) {
                this.endGame('player_blackjack', 'Blackjack! You win!');
            } else {
                this.endGame('dealer_blackjack', 'Dealer has Blackjack!');
            }
        } else {
            this.gameState = 'playing';
            this.updateUI();
        }
    }

    /**
     * Player hits (takes another card)
     */
    async hit() {
        if (this.gameState !== 'playing' || !this.player.canHit()) {
            return;
        }
        
        const card = this.deck.dealCard();
        if (card) {
            this.player.addCard(card);
            this.renderCard(card, this.player);
            card.addDealAnimation();
            
            // Check if player busted
            if (this.player.checkBusted()) {
                setTimeout(() => {
                    this.endGame('player_bust', 'Busted! You lose!');
                }, 600);
            } else if (this.player.getScore() === 21) {
                // Auto-stand on 21
                setTimeout(() => {
                    this.stand();
                }, 600);
            }
        }
        
        this.updateUI();
    }

    /**
     * Player stands (stops taking cards)
     */
    stand() {
        if (this.gameState !== 'playing') {
            return;
        }
        
        this.player.stand();
        this.gameState = 'dealer_turn';
        this.updateUI();
        
        // Reveal dealer's hidden card and play dealer's turn
        setTimeout(() => {
            this.playDealerTurn();
        }, 500);
    }

    /**
     * Play dealer's turn automatically
     */
    async playDealerTurn() {
        // Reveal dealer's hidden card
        this.dealer.revealAllCards();
        this.updateUI();
        
        // Dealer hits until 17 or higher
        while (this.dealer.shouldDealerHit()) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const card = this.deck.dealCard();
            if (card) {
                this.dealer.addCard(card);
                this.renderCard(card, this.dealer);
                card.addDealAnimation();
                this.updateUI();
            }
            
            if (this.dealer.checkBusted()) {
                break;
            }
        }
        
        // Determine winner after dealer's turn
        setTimeout(() => {
            this.determineWinner();
        }, 1000);
    }

    /**
     * Determine the winner and end the game
     */
    determineWinner() {
        const playerScore = this.player.getScore();
        const dealerScore = this.dealer.getScore();
        
        if (this.dealer.isBusted) {
            this.endGame('dealer_bust', 'Dealer busted! You win!');
        } else if (playerScore > dealerScore) {
            this.endGame('player_win', 'You win!');
        } else if (dealerScore > playerScore) {
            this.endGame('dealer_win', 'Dealer wins!');
        } else {
            this.endGame('push', 'Push! It\'s a tie!');
        }
    }

    /**
     * End the game with a result
     */
    endGame(result, message) {
        this.gameState = 'game_over';
        
        // Calculate winnings
        const bet = this.player.currentBet;
        let winnings = 0;
        
        switch(result) {
            case 'player_blackjack':
                winnings = bet * (1 + GameConfig.RULES.BLACKJACK_PAYOUT); // 3:2 payout for blackjack
                break;
            case 'player_win':
            case 'dealer_bust':
                winnings = bet * (1 + GameConfig.RULES.WIN_PAYOUT); // 1:1 payout
                break;
            case 'push':
                winnings = bet; // Return bet
                break;
            default:
                winnings = 0; // Lose bet
        }
        
        this.player.winChips(winnings);
        
        // Add to game history
        this.gameHistory.push({
            result: result,
            playerScore: this.player.getScore(),
            dealerScore: this.dealer.getScore(),
            bet: bet,
            winnings: winnings,
            timestamp: new Date()
        });
        
        this.showGameResult(message, result);
        this.updateUI();
    }

    /**
     * Show game result message
     */
    showGameResult(message, result) {
        this.elements.statusMessage.textContent = message;
        this.elements.gameStatus.style.display = 'block';
        
        // Add result-specific styling
        this.elements.gameStatus.className = `game-status ${result}`;
    }

    /**
     * Hide game result message
     */
    hideGameResult() {
        this.elements.gameStatus.style.display = 'none';
    }

    /**
     * Update all UI elements
     */
    updateUI() {
        this.updateScores();
        this.updateButtons();
        this.updateChips();
    }

    /**
     * Update score displays
     */
    updateScores() {
        const playerScore = this.player.getScore();
        const dealerScore = this.dealer.getScore();
        
        this.elements.playerScore.textContent = `Score: ${playerScore}`;
        
        // Show dealer score (hide if has hidden cards)
        if (this.dealer.getHiddenCards().length > 0 && this.gameState === 'playing') {
            const visibleScore = this.dealer.getVisibleCards().reduce((sum, card) => sum + card.getValue(), 0);
            this.elements.dealerScore.textContent = `Score: ${visibleScore}+`;
        } else {
            this.elements.dealerScore.textContent = `Score: ${dealerScore}`;
        }
    }

    /**
     * Update button states
     */
    updateButtons() {
        const canPlay = this.gameState === 'playing' && this.player.canHit();
        
        this.elements.hitBtn.disabled = !canPlay;
        this.elements.standBtn.disabled = this.gameState !== 'playing';
        this.elements.dealBtn.disabled = this.gameState === 'dealing';
        
        // Update button text based on game state
        if (this.gameState === 'waiting' || this.gameState === 'game_over') {
            this.elements.dealBtn.textContent = 'New Game';
        } else {
            this.elements.dealBtn.textContent = 'Deal';
        }
    }

    /**
     * Update chip display
     */
    updateChips() {
        this.elements.chipCount.textContent = this.player.chips;
        this.elements.currentBet.textContent = this.player.currentBet;
    }

    /**
     * Show temporary message
     */
    showMessage(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        // Could implement toast notifications here
    }

    /**
     * Get game statistics
     */
    getGameStats() {
        const totalGames = this.gameHistory.length;
        const wins = this.gameHistory.filter(game => 
            ['player_win', 'player_blackjack', 'dealer_bust'].includes(game.result)
        ).length;
        
        return {
            totalGames,
            wins,
            losses: totalGames - wins,
            winRate: totalGames > 0 ? (wins / totalGames * 100).toFixed(1) : 0,
            totalWinnings: this.gameHistory.reduce((sum, game) => sum + game.winnings - game.bet, 0)
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BlackjackGame;
}

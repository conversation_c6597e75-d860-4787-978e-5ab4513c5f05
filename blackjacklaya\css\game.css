/* Enhanced Blackjack Game Styles */

/* Card animations and effects */
.card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
    position: relative;
}

.card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    z-index: 10;
}

.card-dealing {
    animation: dealCard 0.5s ease-out forwards;
}

@keyframes dealCard {
    0% {
        opacity: 0;
        transform: translateY(-100px) scale(0.5) rotateY(180deg);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-20px) scale(0.8) rotateY(90deg);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1) rotateY(0deg);
    }
}

.card-flip {
    animation: flipCard 0.6s ease-in-out forwards;
}

@keyframes flipCard {
    0% {
        transform: rotateY(0deg);
    }
    50% {
        transform: rotateY(90deg);
    }
    100% {
        transform: rotateY(0deg);
    }
}

/* Button enhancements */
.btn {
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

/* Game status animations */
.game-status {
    animation: statusSlideIn 0.5s ease-out forwards;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.2);
}

@keyframes statusSlideIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.game-status.player_win,
.game-status.player_blackjack,
.game-status.dealer_bust {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.9), rgba(34, 153, 84, 0.9));
    border-color: #27ae60;
}

.game-status.dealer_win,
.game-status.player_bust,
.game-status.dealer_blackjack {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.9), rgba(192, 57, 43, 0.9));
    border-color: #e74c3c;
}

.game-status.push {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.9), rgba(230, 126, 34, 0.9));
    border-color: #f39c12;
}

/* Score highlighting */
.score {
    transition: all 0.3s ease;
}

.score.bust {
    color: #e74c3c !important;
    animation: scoreBust 0.5s ease-in-out;
}

.score.blackjack {
    color: #f1c40f !important;
    animation: scoreBlackjack 0.8s ease-in-out;
}

@keyframes scoreBust {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

@keyframes scoreBlackjack {
    0%, 100% { transform: scale(1); }
    25%, 75% { transform: scale(1.1); }
    50% { transform: scale(1.2); }
}

/* Chip counter effects */
.chips {
    transition: all 0.3s ease;
}

.chips.winning {
    animation: chipWin 1s ease-in-out;
}

.chips.losing {
    animation: chipLose 0.5s ease-in-out;
}

@keyframes chipWin {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); color: #27ae60; }
}

@keyframes chipLose {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(0.9); color: #e74c3c; }
}

/* Loading enhancements */
.loading {
    animation: loadingPulse 2s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .card {
        width: 50px;
        height: 75px;
        font-size: 10px;
        padding: 4px;
    }
    
    .card-center {
        font-size: 16px !important;
    }
    
    .btn {
        padding: 12px 25px;
        font-size: 16px;
    }
    
    .score {
        font-size: 20px;
    }
    
    .dealer-area,
    .player-area {
        padding: 0 10px;
    }
}

/* Compact layout for small screens */
.compact-layout .dealer-area {
    top: 20px;
}

.compact-layout .player-area {
    bottom: 120px;
}

.compact-layout .controls {
    bottom: 20px;
}

.compact-layout .chips {
    bottom: 180px;
    right: 20px;
    font-size: 14px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .card {
        border-width: 3px;
        border-color: #000;
    }
    
    .btn {
        border: 2px solid #000;
    }
    
    .game-status {
        border-width: 3px;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .card,
    .btn,
    .score,
    .chips,
    .game-status {
        animation: none !important;
        transition: none !important;
    }
    
    .card:hover {
        transform: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #1a1a1a, #2c2c2c);
    }
    
    .card {
        background: #f8f9fa;
        border-color: #495057;
    }
    
    .card-back {
        background: linear-gradient(45deg, #495057, #343a40);
    }
}

/* Focus styles for accessibility */
.btn:focus,
.card:focus {
    outline: 3px solid #007bff;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .controls,
    .chips,
    .game-status {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .card {
        border: 2px solid black;
        background: white;
        color: black;
    }
}

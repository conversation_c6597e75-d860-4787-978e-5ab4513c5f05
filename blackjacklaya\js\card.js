/**
 * Card class representing a playing card in blackjack game
 * Handles card properties, display, and value calculations
 */
class Card {
    constructor(suit, rank) {
        this.suit = suit;
        this.rank = rank;
        this.isHidden = false;
        this.element = null;
    }

    /**
     * Get the blackjack value of the card
     * Aces are handled separately in hand calculation
     */
    getValue() {
        if (this.rank === 'A') {
            return 11; // Will be adjusted in hand calculation if needed
        } else if (['J', 'Q', 'K'].includes(this.rank)) {
            return 10;
        } else {
            return parseInt(this.rank);
        }
    }

    /**
     * Get the display symbol for the suit
     */
    getSuitSymbol() {
        const symbols = {
            'Hearts': '♥',
            'Diamonds': '♦',
            'Clubs': '♣',
            'Spades': '♠'
        };
        return symbols[this.suit];
    }

    /**
     * Get the color class for the card (red or black)
     */
    getColorClass() {
        return ['Hearts', 'Diamonds'].includes(this.suit) ? 'red' : 'black';
    }

    /**
     * Create DOM element for the card
     */
    createElement() {
        const cardElement = document.createElement('div');
        cardElement.className = `card ${this.getColorClass()}`;
        
        if (this.isHidden) {
            cardElement.className = 'card card-back';
            cardElement.innerHTML = '🂠';
        } else {
            const suitSymbol = this.getSuitSymbol();
            cardElement.innerHTML = `
                <div class="card-top">
                    <div>${this.rank}</div>
                    <div>${suitSymbol}</div>
                </div>
                <div class="card-center" style="font-size: 24px; text-align: center;">
                    ${suitSymbol}
                </div>
                <div class="card-bottom" style="transform: rotate(180deg);">
                    <div>${this.rank}</div>
                    <div>${suitSymbol}</div>
                </div>
            `;
        }
        
        this.element = cardElement;
        return cardElement;
    }

    /**
     * Reveal a hidden card with animation
     */
    reveal() {
        if (this.isHidden && this.element) {
            this.isHidden = false;
            
            // Add flip animation
            this.element.style.transform = 'rotateY(90deg)';
            this.element.style.transition = 'transform 0.3s ease';
            
            setTimeout(() => {
                this.element.className = `card ${this.getColorClass()}`;
                const suitSymbol = this.getSuitSymbol();
                this.element.innerHTML = `
                    <div class="card-top">
                        <div>${this.rank}</div>
                        <div>${suitSymbol}</div>
                    </div>
                    <div class="card-center" style="font-size: 24px; text-align: center;">
                        ${suitSymbol}
                    </div>
                    <div class="card-bottom" style="transform: rotate(180deg);">
                        <div>${this.rank}</div>
                        <div>${suitSymbol}</div>
                    </div>
                `;
                this.element.style.transform = 'rotateY(0deg)';
            }, 150);
        }
    }

    /**
     * Hide the card (show card back)
     */
    hide() {
        this.isHidden = true;
        if (this.element) {
            this.element.className = 'card card-back';
            this.element.innerHTML = '🂠';
        }
    }

    /**
     * Add dealing animation to the card
     */
    addDealAnimation(delay = 0) {
        if (this.element) {
            this.element.style.opacity = '0';
            this.element.style.transform = 'translateY(-100px) scale(0.5)';
            this.element.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                this.element.style.opacity = '1';
                this.element.style.transform = 'translateY(0) scale(1)';
            }, delay);
        }
    }

    /**
     * Get string representation of the card
     */
    toString() {
        return `${this.rank} of ${this.suit}`;
    }

    /**
     * Check if this card is an Ace
     */
    isAce() {
        return this.rank === 'A';
    }

    /**
     * Check if this card is a face card (J, Q, K)
     */
    isFaceCard() {
        return ['J', 'Q', 'K'].includes(this.rank);
    }

    /**
     * Get the full name of the card for accessibility
     */
    getFullName() {
        const rankNames = {
            'A': 'Ace',
            'J': 'Jack',
            'Q': 'Queen',
            'K': 'King'
        };
        
        const rankName = rankNames[this.rank] || this.rank;
        return `${rankName} of ${this.suit}`;
    }

    /**
     * Clone the card
     */
    clone() {
        const clonedCard = new Card(this.suit, this.rank);
        clonedCard.isHidden = this.isHidden;
        return clonedCard;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Card;
}

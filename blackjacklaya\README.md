# Blackjack Game - Laya Air Implementation

A professional blackjack card game built with modern web technologies, featuring smooth animations, responsive design, and comprehensive game mechanics.

## 🎮 Game Features

### Core Gameplay
- **Classic Blackjack Rules**: Get as close to 21 as possible without going over
- **Dealer AI**: Intelligent dealer that follows standard casino rules (hits on soft 17)
- **Multiple Deck Shoe**: Uses 6-deck shoe with automatic reshuffling
- **Blackjack Detection**: Automatic detection and 3:2 payout for natural blackjacks
- **Ace Handling**: Smart Ace value calculation (1 or 11) to optimize hand value

### User Interface
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Smooth Animations**: Card dealing, flipping, and movement animations
- **Visual Feedback**: Score highlighting, button states, and game status messages
- **Accessibility**: Keyboard shortcuts and screen reader support
- **Professional Styling**: Casino-quality visual design with gradient backgrounds

### Game Management
- **Chip System**: Start with $1000, place bets, track winnings
- **Auto-Save**: Automatically saves game progress and chip count
- **Game History**: Tracks wins, losses, and statistics
- **Performance Optimized**: Smooth 60fps gameplay with efficient rendering

## 🚀 Quick Start

### Option 1: Direct File Opening
1. Download all files to a local folder
2. Open `index.html` in any modern web browser
3. Start playing immediately!

### Option 2: Local Server (Recommended)
```bash
# Using Python 3
python -m http.server 8000

# Using Node.js
npx serve .

# Using PHP
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

## 🎯 How to Play

### Basic Rules
1. **Goal**: Get a hand value as close to 21 as possible without exceeding it
2. **Card Values**:
   - Number cards (2-10): Face value
   - Face cards (J, Q, K): 10 points each
   - Aces: 1 or 11 points (automatically optimized)

### Game Flow
1. **Place Bet**: Default bet is $50 (automatically placed)
2. **Initial Deal**: You and dealer each get 2 cards (dealer's second card is face down)
3. **Your Turn**: Choose to "Hit" (take another card) or "Stand" (keep current hand)
4. **Dealer's Turn**: Dealer reveals hidden card and hits until 17 or higher
5. **Winner Determination**: Closest to 21 without going over wins

### Controls
- **Hit**: Click "Hit" button or press `H` or `Space`
- **Stand**: Click "Stand" button or press `S` or `Enter`
- **New Game**: Click "New Game" button or press `N`

### Special Situations
- **Blackjack**: 21 with first 2 cards (pays 3:2)
- **Bust**: Hand value exceeds 21 (automatic loss)
- **Push**: Tie with dealer (bet returned)

## 🛠️ Technical Implementation

### Architecture
```
blackjacklaya/
├── index.html          # Main game page
├── css/
│   └── game.css        # Enhanced styling and animations
├── js/
│   ├── card.js         # Card class and card logic
│   ├── deck.js         # Deck management and shuffling
│   ├── player.js       # Player/dealer hand management
│   ├── game.js         # Main game logic and state management
│   └── main.js         # Initialization and global management
└── README.md           # This file
```

### Key Classes

#### Card Class
- Represents individual playing cards
- Handles card display, animations, and value calculations
- Supports hidden/revealed states for dealer cards

#### Deck Class
- Manages multiple deck shoe (default: 6 decks)
- Implements Fisher-Yates shuffling algorithm
- Handles card dealing and discard pile management

#### Player Class
- Manages player and dealer hands
- Calculates optimal scores with Ace handling
- Tracks betting, chips, and game state

#### BlackjackGame Class
- Main game controller and state machine
- Handles game flow, UI updates, and win/loss determination
- Manages animations and user interactions

### Performance Features
- **Efficient Rendering**: Minimal DOM manipulation
- **Smooth Animations**: CSS3 transforms and transitions
- **Memory Management**: Proper cleanup and object pooling
- **Responsive Design**: Optimized for all screen sizes

## 🎨 Customization

### Styling
Edit `css/game.css` to customize:
- Color schemes and themes
- Animation speeds and effects
- Layout and spacing
- Mobile responsiveness

### Game Rules
Modify `js/game.js` to adjust:
- Betting amounts and chip values
- Deck count and reshuffling rules
- Dealer hitting rules (soft 17)
- Payout ratios

### UI Elements
Update `index.html` to change:
- Button labels and layout
- Score display format
- Game status messages

## 🔧 Browser Compatibility

### Supported Browsers
- **Chrome**: 60+ ✅
- **Firefox**: 55+ ✅
- **Safari**: 12+ ✅
- **Edge**: 79+ ✅
- **Mobile Safari**: iOS 12+ ✅
- **Chrome Mobile**: Android 7+ ✅

### Required Features
- ES6 Classes and Arrow Functions
- CSS3 Transforms and Transitions
- Local Storage API
- Modern DOM APIs

## 📱 Mobile Experience

### Touch Optimizations
- Large, touch-friendly buttons
- Responsive card sizing
- Optimized layout for portrait/landscape
- Smooth touch animations

### Performance
- 60fps animations on modern devices
- Efficient memory usage
- Fast loading times
- Offline capability (after first load)

## 🐛 Troubleshooting

### Common Issues

**Game won't load**
- Ensure all files are in correct directories
- Check browser console for errors
- Try refreshing the page

**Cards not displaying properly**
- Clear browser cache
- Ensure CSS file is loading correctly
- Check for JavaScript errors

**Animations stuttering**
- Close other browser tabs
- Disable browser extensions
- Check device performance

### Debug Mode
Add `?debug=true` to URL to enable debug features:
- Performance monitoring
- Game state inspection
- Developer console commands

## 🎯 Future Enhancements

### Planned Features
- **Sound Effects**: Card dealing and button click sounds
- **Multiple Players**: Support for multiple human players
- **Tournament Mode**: Structured tournament gameplay
- **Statistics Dashboard**: Detailed win/loss analytics
- **Themes**: Multiple visual themes and card designs
- **Achievements**: Unlock system for milestones

### Advanced Features
- **Card Counting Trainer**: Practice card counting skills
- **Strategy Hints**: Basic strategy recommendations
- **Multiplayer Online**: Real-time multiplayer support
- **Progressive Jackpots**: Special bonus rounds

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

---

**Enjoy playing Blackjack! 🎰**

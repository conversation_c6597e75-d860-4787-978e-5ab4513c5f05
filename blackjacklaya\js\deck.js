/**
 * Deck class for managing a deck of playing cards
 * Handles shuffling, dealing, and deck management for blackjack
 */
class Deck {
    constructor(numDecks = 1) {
        this.numDecks = numDecks;
        this.cards = [];
        this.discardPile = [];
        this.suits = ['Hearts', 'Diamonds', 'Clubs', 'Spades'];
        this.ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        this.initialize();
    }

    /**
     * Initialize the deck with all cards
     */
    initialize() {
        this.cards = [];
        this.discardPile = [];
        
        for (let deck = 0; deck < this.numDecks; deck++) {
            for (let suit of this.suits) {
                for (let rank of this.ranks) {
                    this.cards.push(new Card(suit, rank));
                }
            }
        }
        
        this.shuffle();
    }

    /**
     * Shuffle the deck using Fisher-Yates algorithm
     */
    shuffle() {
        for (let i = this.cards.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]];
        }
    }

    /**
     * Deal a card from the top of the deck
     * @param {boolean} hidden - Whether the card should be dealt face down
     * @returns {Card|null} The dealt card or null if deck is empty
     */
    dealCard(hidden = false) {
        if (this.cards.length === 0) {
            this.reshuffleDiscardPile();
        }
        
        if (this.cards.length === 0) {
            console.warn('No cards available to deal');
            return null;
        }
        
        const card = this.cards.pop();
        if (hidden) {
            card.hide();
        }
        
        return card;
    }

    /**
     * Deal multiple cards
     * @param {number} count - Number of cards to deal
     * @param {boolean} hidden - Whether cards should be dealt face down
     * @returns {Card[]} Array of dealt cards
     */
    dealCards(count, hidden = false) {
        const dealtCards = [];
        for (let i = 0; i < count; i++) {
            const card = this.dealCard(hidden);
            if (card) {
                dealtCards.push(card);
            }
        }
        return dealtCards;
    }

    /**
     * Add a card to the discard pile
     * @param {Card} card - Card to discard
     */
    discardCard(card) {
        if (card) {
            this.discardPile.push(card);
        }
    }

    /**
     * Add multiple cards to the discard pile
     * @param {Card[]} cards - Cards to discard
     */
    discardCards(cards) {
        for (let card of cards) {
            this.discardCard(card);
        }
    }

    /**
     * Reshuffle the discard pile back into the deck
     */
    reshuffleDiscardPile() {
        if (this.discardPile.length === 0) {
            console.warn('No cards in discard pile to reshuffle');
            return;
        }
        
        console.log('Reshuffling discard pile...');
        this.cards = [...this.cards, ...this.discardPile];
        this.discardPile = [];
        this.shuffle();
    }

    /**
     * Get the number of cards remaining in the deck
     * @returns {number} Number of cards left
     */
    getCardsRemaining() {
        return this.cards.length;
    }

    /**
     * Get the number of cards in the discard pile
     * @returns {number} Number of discarded cards
     */
    getDiscardedCount() {
        return this.discardPile.length;
    }

    /**
     * Check if the deck needs to be reshuffled
     * @param {number} threshold - Minimum cards before reshuffling (default: 20% of total)
     * @returns {boolean} True if reshuffle is needed
     */
    needsReshuffle(threshold = null) {
        if (threshold === null) {
            const configThreshold = typeof GameConfig !== 'undefined' ?
                GameConfig.RULES.RESHUFFLE_THRESHOLD : 0.2;
            threshold = Math.floor((this.numDecks * 52) * configThreshold);
        }
        return this.cards.length <= threshold;
    }

    /**
     * Reset the deck to initial state
     */
    reset() {
        this.initialize();
    }

    /**
     * Get deck statistics
     * @returns {Object} Deck statistics
     */
    getStats() {
        const totalCards = this.numDecks * 52;
        return {
            totalCards: totalCards,
            cardsRemaining: this.cards.length,
            cardsDiscarded: this.discardPile.length,
            percentageUsed: Math.round(((totalCards - this.cards.length) / totalCards) * 100)
        };
    }

    /**
     * Peek at the top card without dealing it
     * @returns {Card|null} The top card or null if deck is empty
     */
    peekTopCard() {
        return this.cards.length > 0 ? this.cards[this.cards.length - 1] : null;
    }

    /**
     * Check if deck is empty
     * @returns {boolean} True if deck has no cards
     */
    isEmpty() {
        return this.cards.length === 0;
    }

    /**
     * Get a specific card from the deck (for testing purposes)
     * @param {string} suit - Card suit
     * @param {string} rank - Card rank
     * @returns {Card|null} The found card or null
     */
    findCard(suit, rank) {
        const index = this.cards.findIndex(card => 
            card.suit === suit && card.rank === rank
        );
        
        if (index !== -1) {
            return this.cards.splice(index, 1)[0];
        }
        
        return null;
    }

    /**
     * Simulate dealing a specific hand (for testing)
     * @param {Array} cardSpecs - Array of {suit, rank} objects
     * @returns {Card[]} Array of specific cards
     */
    dealSpecificHand(cardSpecs) {
        const hand = [];
        for (let spec of cardSpecs) {
            const card = this.findCard(spec.suit, spec.rank);
            if (card) {
                hand.push(card);
            }
        }
        return hand;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Deck;
}
